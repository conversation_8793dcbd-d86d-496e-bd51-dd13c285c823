import { notify, useAuth } from '@context/index';
import { useMutation, UseMutationOptions, useQuery, UseQueryOptions } from '@tanstack/react-query';
import { useMemo, useState } from 'react';
import { apiConfig, ApiConfigMap, EndpointKey } from './apis/index';
import { apiClient } from './axios/axios-client';
import { PaginatedApiConfig, PaginatedResponse, Pagination } from './types/common-api-types';

interface QueryResult<T> {
  data: T | undefined;
  refetch: () => Promise<any>;
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  statusCode: number;
  status: 'idle' | 'error' | 'loading' | 'success';
}

interface PaginatedQueryResult<T> extends QueryResult<T> {
  isInitialLoading: boolean;
  isPaginatedLoading: boolean;
  isInitialError: boolean;
  pagination: Pagination | undefined;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  statusCode: number;
  fetchNextPage: () => void;
}

interface MutationResult<T, V> {
  data: T | undefined;
  isSuccess: boolean;
  isError: boolean;
  mutate: (variables: V) => void;
  mutateAsync: (variables?: V) => Promise<T>;
  isMutating: boolean;
  statusCode: number;
  status: 'idle' | 'error' | 'loading' | 'success';
}

// Helper type for mutation body (supports FormData or defined mutationBody)
type MutationBody<K extends EndpointKey> = ApiConfigMap[K] extends { mutationBody: infer B } ? B : never;

// Return type for useStitch based on endpoint type
type UseStitchResult<K extends EndpointKey> = ApiConfigMap[K]['type'] extends 'mutation'
  ? MutationResult<ApiConfigMap[K]['responseType'], MutationBody<K>>
  : ApiConfigMap[K]['type'] extends 'paginated'
  ? PaginatedQueryResult<ApiConfigMap[K]['responseType']>
  : QueryResult<ApiConfigMap[K]['responseType']>;

// Interface for query parameters to improve type safety
interface QueryParams {
  [key: string]: string | number | boolean | undefined;
}
interface StitchOptions<K extends EndpointKey> {
  queryParams?: QueryParams;
  page?: number;
  queryOptions?: UseQueryOptions<ApiConfigMap[K]['responseType']>;
  mutationOptions?: UseMutationOptions<ApiConfigMap[K]['responseType'], unknown, MutationBody<K>>;
  disableDefaultNotifier?: boolean;
  /**
   * Optional function to customize the API URL by combining the base path and query parameters.
   * Use this to modify the URL structure, such as adding dynamic segments or restructuring query parameters.
   * @param path The base API path (e.g., '/api/users').
   * @param queryParams The query string (e.g., 'search=example&limit=10') or undefined if no query parameters.
   * @returns The complete URL (e.g., '/api/users/123?search=example').
   * @example
   * ```typescript
   * // Add a dynamic ID to the URL path
   * const options = {
   *   queryParams: { search: 'example' },
   *   urlBuilder: (path, queryParams) => `${path}/123${queryParams ? `?${queryParams}` : ''}`,
   * };
   * // Results in: '/api/users/123?search=example'
   * ```
   */
  urlBuilder?: (path: string, queryParams?: string) => string;
}

/**
 * A hook for making API calls based on endpoint configuration.
 * Returns a QueryResult, PaginatedQueryResult, or MutationResult depending on the endpoint type.
 * @param queryKey The endpoint key (e.g., 'getUser', 'createJournal') from ApiConfigMap.
 * @param options Optional configuration including params, page, query/mutation options, and notifier settings.
 * @returns A result object tailored to the endpoint type (query, paginated, or mutation).
 */
function useStitch<K extends EndpointKey>(queryKey: K, options?: StitchOptions<K>): UseStitchResult<K> {
  const config = apiConfig[queryKey];
  const { session } = useAuth();
  const [currentPage, setCurrentPage] = useState(options?.page || 1);
  const queryParams = options?.queryParams || {};

  const getPath = () => (typeof config.path === 'function' ? (config.path as (params: any) => string)(queryParams) : config.path);

  // Build query parameters
  const getQueryParams = () => {
    const obj: QueryParams = { ...queryParams }; // Include user-provided queryParams

    if (config.type === 'paginated') {
      // Default pagination parameters
      obj.page = currentPage.toString();
      obj.limit = ((config as PaginatedApiConfig<any>).pageSize || 10).toString();

      // Merge additional query parameters from queryParamBuilder if provided
      const paginatedConfig = config as PaginatedApiConfig<any>;
      if (paginatedConfig.queryParamBuilder) {
        Object.assign(obj, paginatedConfig.queryParamBuilder(queryParams));
      }
    }

    // Convert to URLSearchParams, filtering out undefined values
    const params = new URLSearchParams();
    Object.entries(obj).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });
    return params.toString();
  };

  const getUrl = () => {
    const path = getPath();
    const queryString = getQueryParams();
    // Use urlBuilder if provided, otherwise default to standard path + query string
    if (options?.urlBuilder) {
      return options.urlBuilder(path, queryString);
    }
    return queryString ? `${path}?${queryString}` : path;
  };

  const queryFn = async (): Promise<ApiConfigMap[K]['responseType']> => {
    // console.log('>>> GET URL >>', getUrl());
    let headers = {};
    if (config.protected && session?.accessToken) {
      headers = {
        ...headers,
        Authorization: `Bearer ${session.accessToken}`,
      };
    }
    const response = await apiClient({
      url: getUrl(),
      method: config.method,
      headers,
    });
    const data = response.data?.data;
    if (config.type === 'paginated') {
      return {
        ...data,
        hasNextPage: data.pagination?.page < data.pagination?.totalPages,
        hasPreviousPage: data?.pagination?.page > 1,
        statusCode: response.status,
      } as PaginatedResponse<any>;
    }
    return data as ApiConfigMap[K]['responseType'];
  };

  const mutationFn = async (body: ApiConfigMap[K] extends { mutationBody: infer B } ? B | FormData : never): Promise<ApiConfigMap[K]['responseType']> => {
    let headers = {};
    if (body instanceof FormData) {
      headers = {
        'Content-Type': 'multipart/form-data',
      };
    }
    if (config.protected && session?.accessToken) {
      headers = {
        ...headers,
        Authorization: `Bearer ${session.accessToken}`,
      };
    }
    const response = await apiClient({
      url: getUrl(),
      method: config.method,
      data: body,
      headers,
    });
    return response?.data as ApiConfigMap[K]['responseType'];
  };

  const queryResult = useQuery<ApiConfigMap[K]['responseType']>({
    queryKey: [config.baseCacheKey, queryParams],
    queryFn,
    staleTime: config.staleTime,
    enabled: config.type !== 'mutation',
    ...options?.queryOptions,
  });

  const mutationResult = useMutation<ApiConfigMap[K]['responseType'], unknown, ApiConfigMap[K] extends { mutationBody: infer B } ? B : never>({
    mutationFn: mutationFn,
    onError: (error: any) => {
      if (options?.disableDefaultNotifier) return;
      notify.top(error?.response?.data?.message);
    },
    ...options?.mutationOptions,
  });

  return useMemo(() => {
    if (config.type === 'mutation') {
      return {
        data: mutationResult.data,
        isSuccess: mutationResult.isSuccess,
        isError: mutationResult.isError,
        mutate: mutationResult.mutate,
        mutateAsync: mutationResult.mutateAsync,
        isMutating: mutationResult.isPending,
        status: mutationResult.status,
        statusCode: mutationResult.data?.statusCode,
      } as MutationResult<ApiConfigMap[K]['responseType'], ApiConfigMap[K] extends { mutationBody: infer B } ? B : never> as any;
    }

    if (config.type === 'paginated') {
      const paginatedData = queryResult.data as PaginatedResponse<any> | undefined;
      return {
        data: queryResult.data,
        refetch: queryResult.refetch,
        isInitialLoading: queryResult.isLoading,
        isPaginatedLoading: queryResult.isFetching,
        isInitialError: queryResult.isError,
        status: queryResult.status,
        pagination: paginatedData?.pagination,
        hasNextPage: paginatedData?.hasNextPage ?? false,
        hasPreviousPage: paginatedData?.hasPreviousPage ?? false,
        statusCode: paginatedData?.statusCode,
        fetchNextPage: () => {
          if (paginatedData?.hasNextPage) {
            setCurrentPage(prev => prev + 1);
            queryResult.refetch();
          }
        },
      } as PaginatedQueryResult<ApiConfigMap[K]['responseType']>;
    }

    return {
      data: queryResult.data,
      refetch: queryResult.refetch,
      isLoading: queryResult.isLoading,
      isError: queryResult.isError,
      isSuccess: queryResult.isSuccess,
      status: queryResult.status,
      statusCode: queryResult.data?.statusCode,
    } as QueryResult<ApiConfigMap[K]['responseType']>;
  }, [queryResult, mutationResult, config, currentPage, queryParams]);
}

export default useStitch;
