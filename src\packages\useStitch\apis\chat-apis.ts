import { CACHE_APIS } from '../api-base-cache-keys';
import { ApiResponse, BestRecommenedStitcherResponse, QueryApiConfig } from '../types';

const C = CACHE_APIS.CHAT;

export namespace ChatApis {
  export const bestStitchers = 'bestStitchers' as const;
}

export const chatApiConfig = {
  bestStitchers: {
    path: '/chat/recommendation/stichers',
    method: 'GET',
    protected: true,
    responseType: undefined as unknown as ApiResponse<BestRecommenedStitcherResponse>,
    baseCacheKey: C.best_stichers,
    staleTime: 0,
    type: 'query' as const,
  } satisfies QueryApiConfig<BestRecommenedStitcherResponse>,
};
