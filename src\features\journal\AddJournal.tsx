import GalleryIcon from '@assets/svgs/gallery-icon.svg';
import MicIcon from '@assets/svgs/mic-icon.svg';
import { Button, Text, TextInput, View } from '@components/native';
import { ScreenWrapper } from '@components/shared';
import { BounceTap } from '@components/shared/animated';
import { useBottomSheet } from '@components/shared/bottom-sheet/BottomSheet';
import { notify, useTheme } from '@context/index';
import { router } from '@navigation/refs/navigation-ref';
import React, { createRef, useEffect } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { Dimensions, Keyboard, KeyboardAvoidingView, Platform, TextInput as RNTextInput, ScrollView } from 'react-native';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import { MicSheetComponent } from './components/MicSheetComponent';
import JournalLiveMarkdownEditor from './components/markdown';
// import {JournalLiveMarkdownEditorProvider, useMarkdownContext} from './components/markdown/JournalMarkdownContext';
import { Token, useJournalStore } from './store/journal-store';

const windowHeight = Dimensions.get('window').height;
const DEFAULT_OFFSET = 260;

export const AddJournal = () => {
  const { handleImagePick, tokens, setTitle, setInputValue, inputValue, counter, setTokens, handleAddAudio, performCleanups } = useJournalStore();
  const { closeSheet: closeMicSheet, Sheet: MicBottomSheet, openSheet: openMicSheet } = useBottomSheet();
  const { colors } = useTheme();
  const { control, handleSubmit } = useForm({
    defaultValues: {
      heading: '',
    },
  });

  const inputHeight = useSharedValue(windowHeight - DEFAULT_OFFSET);

  useEffect(() => {
    const onKeyboardShow = (e: any) => {
      const height = windowHeight - e.endCoordinates.height - DEFAULT_OFFSET;
      inputHeight.value = withTiming(height, { duration: 250 });
    };

    const onKeyboardHide = () => {
      inputHeight.value = withTiming(windowHeight - DEFAULT_OFFSET, { duration: 250 });
    };

    const showSub = Keyboard.addListener('keyboardDidShow', onKeyboardShow);
    const hideSub = Keyboard.addListener('keyboardDidHide', onKeyboardHide);

    return () => {
      showSub.remove();
      hideSub.remove();
    };
  }, [inputHeight]);

  useEffect(() => {
    return () => {
      performCleanups();
    };
  }, []);

  useEffect(() => {
    setTimeout(() => {
      setInputValue('');
    }, 0);
  }, [tokens.length]);

  const animatedInputStyle = useAnimatedStyle(() => ({
    height: inputHeight.value,
  }));

  const onSubmit: SubmitHandler<{ heading: string }> = data => {
    setTitle(data.heading);
    let hasTokens = tokens.length > 0;
    if (inputValue.trim().length > 0) {
      hasTokens = true;
      // Means there is text in main input.
      const updatedTokens = [...tokens, { type: 'text-input', value: inputValue, ref: createRef<RNTextInput>() }] as Token[];
      setTokens(updatedTokens);
    }
    if (!hasTokens) {
      notify.top('Please add some content to your journal description before saving.');
      return;
    }
    router.navigate('ChooseJournalType');
  };

  return (
    <>
      <ScreenWrapper title="Add Journal" style={{ flex: 1, paddingHorizontal: 20, paddingVertical: 15 }}>
        <KeyboardAvoidingView style={{ flex: 1 }} behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
          <ScrollView showsVerticalScrollIndicator={false} bounces={false} contentContainerStyle={{ flexGrow: 1 }}>
            <TextInput
              rules={{
                required: 'Journal Heading is required',
                maxLength: {
                  value: 50,
                  message: 'Heading cannot be more than 50 characters',
                },
              }}
              control={control}
              labelType="background"
              name="heading"
              label="Journal Heading"
            />
            <View style={{ flex: 1, marginBottom: 20, marginTop: 10 }}>
              <Animated.View style={[animatedInputStyle]}>
                <JournalLiveMarkdownEditor />
                <Text ta="right" color={counter.charCount + inputValue.length > 200 ? 'negative50' : 'neutral80'} fs="10" mt={8}>
                  {counter.charCount + inputValue.length} / 200
                </Text>
                <View pt={20} display="flex" fd="row" gap={20} pl={2}>
                  <BounceTap disabled={counter.audioCount >= 1} onPress={openMicSheet}>
                    <MicIcon stroke={colors.purple700} />
                  </BounceTap>
                  <BounceTap disabled={counter.imageCount >= 1} onPress={handleImagePick}>
                    <GalleryIcon stroke={colors.purple700} />
                  </BounceTap>
                </View>

                <Button isDisabled={counter.charCount > 100} mt={20} onPress={handleSubmit(onSubmit)}>
                  SAVE
                </Button>
              </Animated.View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </ScreenWrapper>
      <MicBottomSheet wrapWithSheetView>
        <MicSheetComponent onDone={payload => handleAddAudio({ filePath: payload.filePath })} closeSheet={closeMicSheet} colors={colors} openSheet={openMicSheet} />
      </MicBottomSheet>
    </>
  );
};
