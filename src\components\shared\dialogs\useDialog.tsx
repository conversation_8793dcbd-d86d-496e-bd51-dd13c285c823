import TickPolygonIcon from '@assets/svgs/tick-polygon-icon.svg';
import { Button, Text, View } from '@components/native';
import { useTheme } from '@context/index';
import useStitch from '@packages/useStitch';
import { MotiView } from 'moti';
import { memo, useState } from 'react';
import DialogComponent from 'react-native-dialog';

type options = {
  onClose?: () => void;
};
export const useDialog = (options?: options) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const openDialog = () => {
    setIsDialogOpen(true);
  };
  const closeDialog = () => {
    setIsDialogOpen(false);
    if (options?.onClose) {
      options.onClose();
    }
  };

  const Dialog: React.FC<{ children?: React.ReactNode }> = ({ children }) => {
    return (
      <DialogComponent.Container onBackdropPress={closeDialog} visible={isDialogOpen}>
        <View>{children}</View>
      </DialogComponent.Container>
    );
  };

  return {
    Dialog,
    openDialog,
    closeDialog,
  };
};

export const SuccessDialog: React.FC<{ title?: string; isOpen?: boolean }> = memo(({ isOpen = false, title = 'Created Successfully !' }) => {
  const { colors } = useTheme();

  return (
    <DialogComponent.Container blurStyle={{ backgroundColor: colors.orange }} visible={isOpen}>
      <View mih={220} display="flex" fd="column" ai="center" py={20}>
        <MotiView
          from={{
            rotate: '0deg', // Start at 0 degrees
          }}
          animate={{
            rotate: '360deg', // Rotate to 360 degrees (full spin)
          }}
          transition={{
            type: 'timing',
            duration: 1000, // Each rotation takes 1 second
            repeatReverse: false, // Don't reverse the animation
          }}
          onDidAnimate={() =>
            // After the looping animation completes, transition to 0deg
            Promise.resolve({
              rotate: '0deg',
            })
          }>
          <TickPolygonIcon />
        </MotiView>
        <View>
          <Text ff="PlayfairDisplay-SemiBold" fs="26" ta="center" mt={20}>
            {title}
          </Text>
        </View>
      </View>
    </DialogComponent.Container>
  );
});

type DeleteJournalDialog = {
  journalCatId?: string;
  journalId?: string;
  isOpen?: boolean;
  onCancel?: () => void;
  onSuccess?: () => void;
};
export const DeleteJournalDialog: React.FC<DeleteJournalDialog> = memo(({ onSuccess, onCancel, journalId, journalCatId, isOpen = false }) => {
  const { refetch } = useStitch('journalFeedListByCategory', { queryOptions: { enabled: false } as any, queryParams: { category: journalCatId } });
  const { refetch: refetchAllCategory } = useStitch('journalFeedListByCategory', {
    queryOptions: { enabled: false } as any,
    queryParams: {},
  });
  const { colors } = useTheme();
  const { mutate, isMutating } = useStitch('deleteJournal', {
    urlBuilder: path => `${path}/${journalId}`,
    mutationOptions: {
      onSuccess: () => {
        onSuccess?.();
        refetch();
        refetchAllCategory();
      },
    },
  });

  return (
    <DialogComponent.Container blurStyle={{ backgroundColor: colors.lightBlue }} visible={isOpen}>
      <View display="flex" fd="column" ai="center" px={20}>
        <Text ff="PlayfairDisplay-SemiBold" fs="26" ta="center" mb={20}>
          Are you sure you want to delete this journal ?
        </Text>
        <View flexCenterRow mt={20} gap={10} mb={30}>
          <Button bg="white" color="purple700" bc="purple700" bw={1} isHorizontalAligned onPress={() => onCancel?.()}>
            Cancel
          </Button>
          <Button isLoading={isMutating} bg="negative50" isHorizontalAligned onPress={() => mutate({})}>
            Yes
          </Button>
        </View>
      </View>
    </DialogComponent.Container>
  );
});
