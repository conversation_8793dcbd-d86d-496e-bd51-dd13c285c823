import { ENV } from '@config/env';
import { KeychainStoreKeys } from '@constants/secure-store-keys';
import axios from 'axios';
import * as Keychain from 'react-native-keychain';
import { apiConfig } from '../apis';

const BASE_URL = ENV.BACKEND_URL?.toString() ?? 'http://localhost:3000/api';
const ACCESS_TOKEN: string | null = null;

// Function to get the current refresh token from Keychain
const getRefreshToken = async (): Promise<string | null> => {
  try {
    const credentials = await Keychain.getGenericPassword({
      service: KeychainStoreKeys.REFRESH_TOKEN,
    });
    return credentials ? credentials.password : null;
  } catch (error) {
    console.error('Failed to retrieve refresh token', error);
    return null;
  }
};

// Function to refresh the access token
const refreshAccessToken = async (): Promise<string> => {
  const refreshToken = await getRefreshToken();
  if (!refreshToken) {
    throw new Error('No refresh token available');
  }

  try {
    const response = await axios.post(`${BASE_URL}/auth/refresh`, {
      refreshToken,
    });
    return response.data.accessToken;
  } catch (error) {
    console.error('Failed to refresh access token', error);
    throw error;
  }
};

export const apiClient = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add Authorization header
apiClient.interceptors.request.use(async config => {
  const url = config.url?.replace(BASE_URL, '')?.split('?')[0] || '';
  const match = Object.values(apiConfig).find(c => (typeof c.path === 'string' ? c.path === url : false));

  if (match?.protected) {
    const token = config.headers.Authorization?.toString()?.split(' ')[1];
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
  }

  return config;
});

// Response interceptor to refresh token on 401
apiClient.interceptors.response.use(
  response => response,
  async error => {
    const originalRequest = error.config;
    // console.log('AXIOS CLIENT >>', error?.response);
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      try {
        const newAccessToken = await refreshAccessToken();
        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
        return apiClient(originalRequest);
      } catch (refreshError) {
        console.error('Token refresh failed', refreshError);
        return Promise.reject(refreshError);
      }
    }
    // if (error?.response?.status >= 400) {
    //   notify.top(JSON.stringify(error?.response, null, 2), 'info', 12000);
    // }

    return Promise.reject(error);
  },
);
