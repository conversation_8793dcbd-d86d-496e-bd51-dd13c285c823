import { ThemeColors } from '@/types/color-types';
import PinIcon from '@assets/svgs/pin-icon.svg';
import SearchIcon from '@assets/svgs/search-icon.svg';
import { Button, Image, Text, View } from '@components/native';
import { useDialog } from '@components/shared';
import { BounceTap, Ripple } from '@components/shared/animated';
import { BestStichersHorizontalSkeleton } from '@components/shared/animated/skeletons';
import { useTheme } from '@context/index';
import { router } from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import { FlashList } from '@shopify/flash-list';
import React, { memo, useCallback, useMemo, useState } from 'react';
import { Dimensions, FlatList, Pressable } from 'react-native';

const { width } = Dimensions.get('screen');

const mockChatItemObj = {
  name: 'SU101',
  lastMsg: 'Stand up for what you believe in you believe in asdas',
  unreadMsgCount: 2,
  pinned: true,
  isOnline: true,
} as const;

type ChatItem = typeof mockChatItemObj;

export const ConversationsScreen = memo(() => {
  const { colors } = useTheme();
  const { refetch: refetchBestStitchers } = useStitch('bestStitchers', { queryOptions: { enabled: false } } as any);
  const [currentTab, setCurrentTab] = useState<'chat' | 'requests'>('chat');

  const contentContainerStyle = useMemo(
    () => ({
      paddingBottom: 80,
    }),
    [],
  );

  const handleRefresh = useCallback(() => {
    refetchBestStitchers();
  }, [refetchBestStitchers]);

  return (
    <View flex={1} bg="background">
      <FlashList
        onRefresh={handleRefresh}
        ItemSeparatorComponent={() => <View h={8} />}
        refreshing={false}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={() => <ConversationsHeader setCurrentTab={setCurrentTab} currentTab={currentTab} />}
        data={Array.from({ length: 10 }).fill(mockChatItemObj)}
        contentContainerStyle={contentContainerStyle}
        renderItem={({ item, index }) => <ChatItem colors={colors} item={item as any} key={index} />}
      />
    </View>
  );
});

type ConversationsHeaderProps = {
  setCurrentTab: React.Dispatch<React.SetStateAction<'chat' | 'requests'>>;
  currentTab: 'chat' | 'requests';
};

const ConversationsHeader = memo(({ currentTab, setCurrentTab }: ConversationsHeaderProps) => {
  const { colors } = useTheme();
  const { openDialog, Dialog } = useDialog();
  const { data, isLoading, isError } = useStitch('bestStitchers');

  const contentContainerStyle = useMemo(
    () => ({
      paddingHorizontal: 20,
      marginBottom: 20,
      paddingEnd: width / 6,
    }),
    [],
  );

  return (
    <View bg="white">
      <View px={20} pt={14}>
        <Pressable onPress={() => router.navigate('ChatSearch')}>
          <View bg="white" bc="purpleLight" bw={1} display="flex" fd="row" jc="space-between" br={16} px={20} py={14}>
            <Text color="neutral40" fs="14">
              Search
            </Text>
            <SearchIcon stroke={colors.purple600} />
          </View>
        </Pressable>
        <Text my={14} ml={3} fs="14" fw="600" color="neutral70">
          Best Stitchers
        </Text>
      </View>
      {isLoading ? (
        <BestStichersHorizontalSkeleton />
      ) : isError || data?.data?.length === 0 ? (
        <View px={20} mb={20}>
          <Text ta="center" fs="12" color="neutral70">
            No best stitcher at the moment
          </Text>
        </View>
      ) : (
        <FlatList
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          pagingEnabled
          contentContainerStyle={contentContainerStyle}
          ItemSeparatorComponent={() => <View w={12} />}
          data={data?.data ?? []}
          renderItem={({ item, index }) => (
            <BounceTap onPress={() => {}} pressedScale={0.97} key={index}>
              <View shadow="md" bg="white" mih={120} br={14} bc="neutral10" bw={1} mah={180} miw={130} maw={150} py={10} display="flex" fd="column" jc="center" ai="center">
                <View>
                  <View size={64} mb={20} br={100} bg="orange">
                    <Image source={{ uri: item.avatar }} size={64} />
                  </View>
                  <View bg="lightBlue" pos="absolute" bottom={2} px={5} py={3} br={7} bw={1} bc="neutral20">
                    <Text fs="10">{item.totalHealCount.toString()} Healed</Text>
                  </View>
                </View>
                <View mt={5} px={10}>
                  <Text numberOfLines={1} fs="12" fw="500" color="neutral80">
                    {item.username}
                  </Text>
                </View>
                <View flexCenterRow mt={10} px={10}>
                  <Button h={32} py={0} bg="purpleLight" br={10} color="neutral80" isHorizontalAligned onPress={() => {}}>
                    Chat
                  </Button>
                </View>
              </View>
            </BounceTap>
          )}
        />
      )}
      <View display="flex" pl={20} mb={14} ai="center" gap={10} px={14} fd="row">
        <TabHeader onPress={() => setCurrentTab('chat')} title="Chats" count={0} isActive={currentTab === 'chat'} />
        <TabHeader onPress={() => setCurrentTab('requests')} title="Requests" count={2} isActive={currentTab === 'requests'} />
      </View>
    </View>
  );
});

type TabHeaderProps = {
  title: string;
  count: number;
  isActive: boolean;
  onPress?: () => void;
};

const TabHeader = memo(({ count = 0, onPress, isActive = false, title }: TabHeaderProps) => {
  return (
    <BounceTap onPress={onPress}>
      <View bc="neutral20" style={{ backgroundColor: isActive ? '#FEEBE491' : '' }} flexCenterRow gap={5} bw={1} px={25} py={4} br={50}>
        <Text fs="12" color="neutral80" fw="600">
          {title}
        </Text>
        {count > 0 && (
          <View bg="orange" p={2} br={50} disableSizeMatter miw={20} flexCenterRow>
            <Text fs="8">{count}</Text>
          </View>
        )}
      </View>
    </BounceTap>
  );
});

const AVATAR_SIZE = 45;

type ChatItemProps = {
  item: ChatItem;
  colors: ThemeColors;
};

const ChatItem = memo(({ item, colors }: ChatItemProps) => {
  const handlePress = useCallback(() => {
    router.navigate('Chat');
  }, []);

  return (
    <Ripple onPress={handlePress} rippleColor={colors.neutral10}>
      <View disableSizeMatter display="flex" fd="row" ai="center" w={width} mih={50} mah={80} px={20} py={10}>
        <View size={AVATAR_SIZE} br={100} bg="orange" />
        <View disableSizeMatter flex={1} display="flex" fd="row" jc="space-between" ai="center" px={10} gap={10}>
          <View display="flex" fd="column" gap={4} flexGrow={1} flexShrink={1} maw={width - AVATAR_SIZE - 80}>
            <Text color="neutral80" fs="12" fw="600">
              {item.name}
            </Text>
            <Text numberOfLines={1} color="neutral50" fs="14">
              {item.lastMsg}
            </Text>
          </View>
          <View disableSizeMatter display="flex" fd="row" ai="center" gap={10}>
            {item.unreadMsgCount > 0 && (
              <View size={20} flexCenterRow br={100} bg="orange">
                <Text color="neutral80" fs="12">
                  {item.unreadMsgCount}
                </Text>
              </View>
            )}
            {item.pinned && <PinIcon />}
          </View>
        </View>
      </View>
    </Ripple>
  );
});

const RequestsFlatList = memo(() => {
  return (
    <View>
      <Text></Text>
    </View>
  );
});
