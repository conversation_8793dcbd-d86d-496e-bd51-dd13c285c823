import { Button, Image, Text, TextInput, View } from '@components/native';
import { ScreenWrapper } from '@components/shared';
import { BounceTap, LoaderCentered } from '@components/shared/animated';
import useStitch from '@packages/useStitch';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Dimensions, ScrollView } from 'react-native';
import { SimpleGrid } from 'react-native-super-grid';

const { width } = Dimensions.get('screen');
export const CreateJournalType = () => {
  const { data, isLoading, isError } = useStitch('journalCategoryCovers');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const { control, handleSubmit, setValue } = useForm({
    defaultValues: {
      albumTitle: '',
    },
  });
  return (
    <ScreenWrapper title="Create new album">
      <View flex={1} p={20}>
        <TextInput control={control} name="albumTitle" label="Album Title" labelType="background" />
        <Text mt={20}>Choose Album Cover</Text>
        <View flex={1} mt={15}>
          {isLoading ? (
            <LoaderCentered />
          ) : (
            <ScrollView bounces={false}>
              <SimpleGrid
                listKey="create-journal-type"
                itemDimension={width / 4}
                spacing={0}
                data={data?.data ?? []}
                renderItem={({ item, index }) => {
                  return (
                    <BounceTap onPress={() => setSelectedIndex(index)}>
                      <Image source={{ uri: item.url }} h={200} w={200} />
                      {/* <View
                        bg="black"
                        mr={14}
                        mb={14}
                        bbr={20}
                        btr={20}
                        bw={selectedIndex == index ? 2 : 0}
                        bc={selectedIndex == index ? 'positive60' : 'transparent'}
                        h={width / 3.6}
                        key={index}>
                        <Image style={{ width: '100%', height: '100%', borderTopRightRadius: 20, borderBottomRightRadius: 20 }} source={require('@assets/images/lost-journal-bg.png')} />
                      </View> */}
                    </BounceTap>
                  );
                }}
              />
            </ScrollView>
          )}
        </View>
        <Button my={20} onPress={() => {}}>
          SAVE
        </Button>
      </View>
    </ScreenWrapper>
  );
};
