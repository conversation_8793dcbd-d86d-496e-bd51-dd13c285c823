import HandWave from '@assets/svgs/hand-wavesvg.svg';
import { Text, View } from '@components/native';
import { ScreenWrapper } from '@components/shared';

export const ChatScreen = () => {
  return (
    <ScreenWrapper hideBackButton={false} title="@sunshine">
      <View flex={1} gap={40} display="flex" fd="column" ai="center" mt={50}>
        <Text fw="500" color="neutral80">
          Say Hi! and start a conversation.
        </Text>
        <HandWave />
      </View>
    </ScreenWrapper>
  );
};
