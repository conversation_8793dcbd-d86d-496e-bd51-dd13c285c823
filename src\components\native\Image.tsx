import { ThemeColorKeys } from '@/types/color-types';
import { useTheme } from '@context/index';
import React, { forwardRef, useState } from 'react';
import { DimensionValue, ImageStyle, Pressable, Image as RNImage, ImageProps as RNImageProps, View } from 'react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import Evilicons from 'react-native-vector-icons/EvilIcons';
import { Text } from './Text';

export interface ImageProps extends RNImageProps {
  w?: number | DimensionValue;
  h?: number | DimensionValue;
  mih?: number | DimensionValue;
  miw?: number | DimensionValue;
  mah?: number | DimensionValue;
  maw?: number | DimensionValue;
  size?: number;
  my?: number;

  br?: number;
  btl?: number;
  btr?: number;
  bbl?: number;
  bbr?: number;

  bw?: number;
  bc?: ThemeColorKeys;

  top?: DimensionValue;
  bottom?: DimensionValue;
  left?: DimensionValue;
  right?: DimensionValue;
  pos?: 'relative' | 'absolute';

  bg?: ThemeColorKeys;
  tint?: ThemeColorKeys;

  opacity?: number;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none';

  disableSizeMatter?: boolean;
}

export const Image = forwardRef<RNImage, ImageProps>(
  (
    {
      w,
      h,
      mih,
      miw,
      mah,
      maw,
      size,
      br,
      btl,
      btr,
      bbl,
      bbr,
      bw,
      bc,
      bg,
      tint,
      pos,
      top,
      bottom,
      left,
      right,
      my,
      resizeMode,
      objectFit,
      opacity,
      style,
      source,
      disableSizeMatter = false,
      ...props
    },
    ref,
  ) => {
    const [isError, setIsError] = useState(false);
    const [retryKey, setRetryKey] = useState(Date.now());
    const { colors } = useTheme();

    const isRemoteImage = typeof source === 'object' && 'uri' in source;

    const defaultStyles = {
      ...(w !== undefined && {
        width: disableSizeMatter ? w : typeof w == 'string' ? w : moderateScale(Number(w)),
      }),
      ...(h !== undefined && {
        height: disableSizeMatter ? h : typeof h == 'string' ? h : verticalScale(Number(h)),
      }),
      ...(mih !== undefined && {
        minHeight: disableSizeMatter ? mih : verticalScale(Number(mih)),
      }),
      ...(miw !== undefined && {
        minWidth: disableSizeMatter ? miw : moderateScale(Number(miw), 0.5),
      }),
      ...(mah !== undefined && {
        maxHeight: disableSizeMatter ? mah : verticalScale(Number(mah)),
      }),
      ...(maw !== undefined && {
        maxWidth: disableSizeMatter ? maw : moderateScale(Number(maw), 0.5),
      }),
      ...(size !== undefined && {
        height: size,
        width: size,
      }),
      ...(br !== undefined && { borderRadius: br }),
      ...(btl !== undefined && { borderTopLeftRadius: btl }),
      ...(btr !== undefined && { borderTopRightRadius: btr }),
      ...(bbl !== undefined && { borderBottomLeftRadius: bbl }),
      ...(bbr !== undefined && { borderBottomRightRadius: bbr }),
      ...(bw !== undefined && { borderWidth: bw }),
      ...(bc !== undefined && { borderColor: colors[bc] ?? bc }),
      ...(bg !== undefined && { backgroundColor: colors[bg] ?? bg }),
      ...(tint !== undefined && { tintColor: colors[tint] ?? tint }),
      ...(resizeMode !== undefined && { resizeMode }),
      ...(objectFit !== undefined && { objectFit }),
      ...(opacity !== undefined && { opacity }),
      ...(pos !== undefined && { position: pos }),
      ...(top !== undefined && { top }),
      ...(bottom !== undefined && { bottom }),
      ...(left !== undefined && { left }),
      ...(right !== undefined && { right }),
      ...(my !== undefined && { marginVertical: verticalScale(my) }),
    } as ImageStyle;

    const handleRetry = () => {
      setIsError(false);
      setRetryKey(Date.now()); // triggers re-render
    };

    const showBrokenTile = isError && isRemoteImage;

    return (
      <>
        {!showBrokenTile ? (
          <RNImage key={retryKey} ref={ref} style={[defaultStyles, style]} source={source} onError={() => setIsError(true)} {...props} />
        ) : (
          <Pressable onPress={showBrokenTile ? handleRetry : undefined} style={[defaultStyles, style]}>
            <View
              style={{
                flex: 1,
                alignItems: 'center',
                justifyContent: 'center',
                marginTop: 10,
              }}>
              <Evilicons name="redo" size={25} />
              <Text fs="8" mt={5}>
                Unable to load image
              </Text>
            </View>
          </Pressable>
        )}
      </>
    );
  },
);

Image.displayName = 'Image';
