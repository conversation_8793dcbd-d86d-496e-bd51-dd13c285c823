import { TypeEnum } from '@features/journal/store/journal-store';

export interface JournalCategoryItem {
  id: string;
  title: string;
  type: string;
  bgImage: string;
  totalCount: number;
}

export type JournalCategoryListResponse = JournalCategoryItem[];
export interface JournalCategoryCoverItem {
  url: string;
  id: string;
}

export type JournalCategoryCoversResponse = JournalCategoryCoverItem[];

export type JournalDetailsToken = {
  type: TypeEnum;
  value: string;
};

export type JournalDetailsResponse = {
  id: string;
  title: string;
  description: JournalDetailsToken[];
  category: JournalCategoryItem;
};

export type JournalFeedItem = {
  id: string;
  title: string;
  summary: string;
  category: JournalCategoryItem;
  createdAt: string;
};
