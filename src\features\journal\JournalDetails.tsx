import DeleteIcon from '@assets/svgs/delete-icon.svg';
import PencilIcon from '@assets/svgs/pencil-icon.svg';
import ShareIcon from '@assets/svgs/share-icon.svg';
import { Text, View } from '@components/native';
import { Image } from '@components/native/Image';
import { DeleteJournalDialog, ScreenWrapper } from '@components/shared';
import { BounceTap, LoaderCentered } from '@components/shared/animated';
import { useTheme } from '@context/index';
import { router } from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import React, { useState } from 'react';
import { ScrollView } from 'react-native';
import AudioInputToken from './components/markdown/tokens/AudioInputToken';

export const JournalDetails = () => {
  const params = router.params('JournalDetails');
  const { data, isError, isLoading } = useStitch('journalDetailsById', {
    urlBuilder: path => `${path}/${params?.journalId}`,
  });
  const description = data?.data?.description ?? [];

  return (
    <>
      <ScreenWrapper title={<Header catId={data?.data?.category.id} id={data?.data?.id ?? ''} />}>
        {isLoading ? (
          <LoaderCentered />
        ) : (
          <ScrollView>
            <View p={20} bg="background" flex={1}>
              <Text mb={20} fw="500" fs="20" color="neutral80">
                {data?.data?.title}
              </Text>
              {description.map((item, index) => {
                switch (item.type) {
                  case 'text-input':
                    return <Text color="neutral80" fs="14" ml={4} key={index} children={item.value} />;
                  case 'image-url':
                    return <Image key={index} my={5} h={180} bg="neutral10" br={14} source={{ uri: item.value }} />;
                  case 'audio-url':
                    return <AudioInputToken key={index} value={item.value} />;
                  default:
                    return <React.Fragment key={index} />;
                }
              })}
            </View>
          </ScrollView>
        )}
      </ScreenWrapper>
    </>
  );
};

const Header = ({ id, catId }: { id: string; catId?: string }) => {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const params = router.params('JournalDetails');
  const { colors } = useTheme();
  return (
    <View flex={1} display="flex" fd="row" ai="center" jc="space-between">
      <View>
        <Text fw="600" color="neutral90" fs="18" numberOfLines={1}>
          {params?.type}
        </Text>
        <View size={4} />
        <Text fs="14" color="neutral40" numberOfLines={1}>
          {params?.publishedOn}
        </Text>
      </View>
      <View gap={20} display="flex" fd="row">
        <BounceTap
          onPress={() => {
            if (id != '') {
              router.navigate('EditJournal', { journalId: id });
            }
          }}>
          <View px={2} py={10} flexCenterRow>
            <PencilIcon />
          </View>
        </BounceTap>
        <BounceTap onPress={() => {}}>
          <View px={2} py={10} flexCenterRow>
            <ShareIcon />
          </View>
        </BounceTap>
        <BounceTap onPress={() => setIsDeleteDialogOpen(true)}>
          <View px={2} py={10} flexCenterRow>
            <DeleteIcon fill={colors.neutral60} />
          </View>
        </BounceTap>
      </View>
      <DeleteJournalDialog
        onSuccess={() => {
          setIsDeleteDialogOpen(false);
          router.back();
        }}
        onCancel={() => setIsDeleteDialogOpen(false)}
        journalCatId={catId}
        journalId={params?.journalId}
        isOpen={isDeleteDialogOpen}
      />
    </View>
  );
};
