import messaging from '@react-native-firebase/messaging';
import { createContext, useCallback, useContext, useEffect, useMemo } from 'react';

// Define types for the context
interface FirebaseContextType {
  getFcmToken: () => Promise<string | null>;
  listenToForegroundNotifications: () => () => void;
  listenToBackgroundNotifications: () => void;
}

// Create context with default values
const FirebaseContext = createContext<FirebaseContextType>({
  getFcmToken: async () => null,
  listenToForegroundNotifications: () => () => {},
  listenToBackgroundNotifications: () => {},
});

// Props type for the provider
interface FirebaseProviderProps {
  children: React.ReactNode;
}

const FirebaseProvider: React.FC<FirebaseProviderProps> = ({ children }) => {
  // Get FCM token with error handling
  const getFcmToken = useCallback(async (): Promise<string | null> => {
    try {
      const authStatus = await messaging().requestPermission();
      const enabled = authStatus === messaging.AuthorizationStatus.AUTHORIZED || authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (!enabled) {
        console.warn('Notification permission not granted');
        return null;
      }

      const fcmToken = await messaging().getToken();
      if (fcmToken) {
        console.log('FCM Token:', fcmToken);
        return fcmToken;
      } else {
        console.warn('No FCM token available');
        return null;
      }
    } catch (error) {
      console.error('Error getting FCM token:', error);
      return null;
    }
  }, []);

  // Handle foreground notifications
  const listenToForegroundNotifications = useCallback(() => {
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      console.log('Foreground Message:', JSON.stringify(remoteMessage, null, 2));
      // Add your notification handling logic here
    });
    return unsubscribe;
  }, []);

  // Handle background notifications
  const listenToBackgroundNotifications = useCallback(() => {
    messaging().setBackgroundMessageHandler(async remoteMessage => {
      console.log('Background Message:', JSON.stringify(remoteMessage, null, 2));
      // Add your background notification handling logic here
    });
  }, []);

  // Initialize notifications when component mounts
  useEffect(() => {
    // Request permission and get token
    getFcmToken();

    // Set up foreground notification listener
    const unsubscribeForeground = listenToForegroundNotifications();

    // Set up background notification handler
    listenToBackgroundNotifications();

    // Cleanup
    return () => {
      unsubscribeForeground();
    };
  }, [getFcmToken, listenToForegroundNotifications, listenToBackgroundNotifications]);

  // Memoize context value
  const contextValue = useMemo(
    () => ({
      getFcmToken,
      listenToForegroundNotifications,
      listenToBackgroundNotifications,
    }),
    [getFcmToken, listenToForegroundNotifications, listenToBackgroundNotifications],
  );

  return <FirebaseContext.Provider value={contextValue}>{children}</FirebaseContext.Provider>;
};

// Export a custom hook for using the context
export const useFirebase = () => {
  const context = useContext(FirebaseContext);
  if (!context) {
    throw new Error('useFirebase must be used within a FirebaseProvider');
  }
  return context;
};

export default FirebaseProvider;

// async function onDisplayNotification() {
//   // Request permissions (required for iOS)
//   await notifee.requestPermission();

//   // Create a channel (required for Android)
//   const channelId = await notifee.createChannel({
//     id: 'default',
//     name: 'Default Channel',
//   });

//   // Display a notification
//   await notifee.displayNotification({
//     title: 'Notification Title',
//     body: 'Main body content of the notification',
//     android: {
//       channelId,
//       // pressAction is needed if you want the notification to open the app when pressed
//       pressAction: {
//         id: 'default',
//       },
//     },
//   });
// }

// Create notification channel for chat notifications
// async function createNotificationChannel() {
//   if (Platform.OS === 'android') {
//     return await notifee.createChannel({
//       id: 'chat_channel',
//       name: 'Chat Notifications',
//       sound: 'default',
//       vibration: true,
//       importance: AndroidImportance.HIGH,
//     });
//   }
//   return null;
// }

// async function requestNotificationPermission() {
//   if (Platform.OS === 'android' && Platform.Version >= 33) {
//     const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS, {
//       title: 'Notification Permission',
//       message: 'Allow Stitch to send notifications?',
//       buttonPositive: 'OK',
//     });
//     return granted === PermissionsAndroid.RESULTS.GRANTED;
//   }
//   return true;
// }

// // Display a chat-like notification
// async function onDisplayNotification(senderName: string, messageBody: string, conversationId: string, avatarUrl?: string) {
//   if (Platform.OS !== 'android') return;

//   // Request permissions
//   const hasPermission = await requestNotificationPermission();
//   if (!hasPermission) {
//     console.warn('Notification permission not granted');
//     return;
//   }

//   // Create channel

//   try {
//     const channelId = await createNotificationChannel();
//     if (typeof channelId !== 'string') return;
//     await notifee.displayNotification({
//       title: senderName,
//       body: messageBody,
//       android: {
//         channelId,
//         largeIcon: avatarUrl || 'drawable/avatar', // Sender's avatar or placeholder
//         style: {
//           type: AndroidStyle.MESSAGING,
//           person: {
//             name: senderName,
//             id: '123',
//             icon: avatarUrl || 'drawable/avatar',
//           },
//           messages: [
//             {
//               text: messageBody,
//               timestamp: Date.now(),
//               person: { name: senderName },
//             },
//           ],
//           group: true, // Enable grouping for multiple messages
//         },
//         groupId: `chat_${conversationId}`, // Group notifications by conversation
//         pressAction: {
//           id: 'default',
//           launchActivity: 'default',
//         },
//         actions: [
//           {
//             title: 'Reply',
//             icon: 'ic_reply',
//             pressAction: {
//               id: 'reply',
//               launchActivity: 'com.stitch.NotificationActionService',
//             },
//             input: {
//               allowFreeFormInput: true,
//               placeholder: 'Type your reply...',
//             },
//           },
//           {
//             title: 'Mark as Read',
//             pressAction: { id: 'mark_as_read' },
//           },
//         ],
//         showTimestamp: true,
//         timestamp: Date.now(),
//       },
//     });

//     // Display group summary notification
//     await notifee.displayNotification({
//       title: 'New Messages',
//       body: `You have new messages from ${senderName}`,
//       android: {
//         channelId,
//         smallIcon: 'ic_chat_notification',
//         groupSummary: true,
//         groupId: `chat_${conversationId}`,
//       },
//     });

//     console.log('Chat notification displayed');
//   } catch (error) {
//     console.error('Error Displaying Notification:', error);
//   }
// }
