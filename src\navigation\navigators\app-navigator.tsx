import AppLogo from '@assets/svgs/app-logo-white.svg';
import { View } from '@components/native';
import { useAuth } from '@context/index';
import { createStackNavigator } from '@react-navigation/stack';
import { lazy, Suspense } from 'react';

const Stack = createStackNavigator();
const AuthScreens = lazy(() => import('./auth-screens'));
const UnAuthScreens = lazy(() => import('./unauth-screens'));

const AppNavigator = () => {
  const { isAuthenticated, isInitialLoading, session } = useAuth();
  if (isInitialLoading) {
    return <SplashScreen />;
  }

  // if (isAuthenticated && !session.isSignupCompleted) {
  //   return (
  //     <Stack.Navigator initialRouteName={SCREENS.GET_STARTED} screenOptions={{ headerShown: false }}>
  //       <Stack.Screen name={SCREENS.GET_STARTED} component={PostAuthAddPersonalDetails} />
  //     </Stack.Navigator>
  //   );
  // }

  if (true) {
    return (
      <Suspense fallback={null}>
        <AuthScreens />
      </Suspense>
    );
  }

  return (
    <Suspense fallback={null}>
      <UnAuthScreens />
    </Suspense>
  );
};

export default AppNavigator;

const SplashScreen = () => {
  return (
    <View flex={1} bg="purple500" flexCenterRow>
      <AppLogo fill="white" />
    </View>
  );
};
